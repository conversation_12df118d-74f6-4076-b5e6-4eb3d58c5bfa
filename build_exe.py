#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客服部绩效数据拉取项目简单打包脚本
"""

import os
import sys
import subprocess
import shutil

def main():
    """简单打包主函数"""
    print("🚀 开始打包...")

    # 检查必要文件
    if not os.path.exists('客服绩效数据同步.py'):
        print("❌ 找不到主程序文件")
        return

    # 安装PyInstaller（如果需要）
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
    except ImportError:
        print("📦 安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

    # 简单打包命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--console",
        "--name=客服绩效数据拉取",
        "客服绩效数据同步.py"
    ]

    print("🔨 开始打包...")
    result = subprocess.run(cmd)

    if result.returncode == 0:
        print("✅ 打包成功！")

        # 复制config.json到dist目录
        if os.path.exists('config.json'):
            shutil.copy2('config.json', 'dist/')
            print("✅ 已复制config.json")

        print(f"📁 文件位置: {os.path.abspath('dist')}")
        print("📋 输出文件:")
        print("  - 客服绩效数据拉取.exe")
        print("  - config.json")
    else:
        print("❌ 打包失败")

if __name__ == '__main__':
    main()

def create_spec_file():
    """创建PyInstaller的spec配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['客服绩效数据同步.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('dingtalk_sheet_utils.py', '.'),
    ],
    hiddenimports=[
        'pandas',
        'openpyxl',
        'requests',
        'urllib3',
        'argparse',
        'json',
        'datetime',
        'time',
        're'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy.testing',
        'scipy',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='客服绩效数据拉取',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

    with open('客服绩效数据拉取.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ 已创建spec配置文件")

def build_exe():
    """执行打包"""
    print("🔨 开始打包exe文件...")

    try:
        # 使用spec文件进行打包，添加更多参数来避免卡住
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",  # 不询问确认
            "--log-level=INFO",  # 显示详细日志
            "客服绩效数据拉取.spec"
        ]

        print(f"📋 执行命令: {' '.join(cmd)}")
        print("⏳ 打包过程可能需要几分钟，请耐心等待...")

        # 使用Popen来实时显示输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            universal_newlines=True
        )

        # 实时显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"  {output.strip()}")

        # 等待进程完成
        return_code = process.poll()

        if return_code == 0:
            print("✅ 打包成功！")
            print(f"📁 exe文件位置: {os.path.abspath('dist/客服绩效数据拉取.exe')}")
            return True
        else:
            print(f"❌ 打包失败！返回码: {return_code}")
            return False

    except Exception as e:
        print(f"❌ 打包过程中发生异常: {e}")
        return False



def copy_config_files():
    """复制配置文件到dist目录"""
    try:
        if os.path.exists('config.json'):
            shutil.copy2('config.json', 'dist/')
            print("✅ 已复制config.json到dist目录")

    except Exception as e:
        print(f"⚠️ 复制配置文件时出错: {e}")

def clean_build_files():
    """清理构建过程中的临时文件"""
    try:
        # 删除build目录
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("🧹 已清理build目录")
        
        # 删除spec文件
        if os.path.exists('客服绩效数据拉取.spec'):
            os.remove('客服绩效数据拉取.spec')
            print("🧹 已清理spec文件")
            
    except Exception as e:
        print(f"⚠️ 清理临时文件时出错: {e}")

def build_exe_simple():
    """简化的打包方法，如果复杂方法失败时使用"""
    print("🔨 使用简化方法打包exe文件...")

    try:
        # 简化的打包命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",  # 打包成单个文件
            "--noconfirm",
            "--console",
            "--name=客服绩效数据拉取",
            "客服绩效数据同步.py"
        ]

        print(f"📋 执行简化命令: {' '.join(cmd)}")
        print("⏳ 简化打包过程，请等待...")

        # 使用Popen来实时显示输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            universal_newlines=True
        )

        # 实时显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"  {output.strip()}")

        # 等待进程完成
        return_code = process.poll()

        if return_code == 0:
            print("✅ 简化打包成功！")
            print(f"📁 exe文件位置: {os.path.abspath('dist/客服绩效数据拉取.exe')}")
            return True
        else:
            print(f"❌ 简化打包失败！返回码: {return_code}")
            return False

    except Exception as e:
        print(f"❌ 简化打包过程中发生异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 客服部绩效数据拉取项目打包工具")
    print("=" * 50)

    # 检查当前目录是否包含必要文件
    required_files = ['客服绩效数据同步.py', 'dingtalk_sheet_utils.py', 'config.json']
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在项目根目录下运行此脚本")
        return

    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 无法安装PyInstaller，打包失败")
            return

    # 询问用户选择打包方式
    print("\n请选择打包方式:")
    print("1. 标准打包 (使用spec文件，功能完整)")
    print("2. 简化打包 (单文件模式，速度更快)")

    choice = input("请输入选择 (1/2，默认为2): ").strip()

    success = False

    if choice == "1":
        # 创建spec文件
        create_spec_file()
        # 执行标准打包
        success = build_exe()
    else:
        # 执行简化打包
        success = build_exe_simple()

    if success:
        # 复制配置文件
        copy_config_files()

        # 清理临时文件
        clean_build_files()

        print("\n" + "=" * 50)
        print("🎉 打包完成！")
        print(f"📁 输出目录: {os.path.abspath('dist')}")
        print("\n文件列表:")
        print("- 客服绩效数据拉取.exe (主程序)")
        if choice == "1":
            print("- config.json (配置文件)")
        else:
            print("注意：简化模式下，请手动将config.json放在exe同目录下")

    else:
        print("❌ 打包失败，请检查错误信息")
        if choice == "1":
            print("💡 建议尝试简化打包模式 (选项2)")
        else:
            print("💡 如果问题持续，请检查Python环境和依赖包")

if __name__ == '__main__':
    main()
